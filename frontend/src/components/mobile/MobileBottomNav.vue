<template>
  <nav class="mobile-bottom-nav">
    <!-- 首頁 -->
    <router-link to="/" class="mobile-nav-item" :class="{ active: isActive('/') }">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
        <polyline points="9,22 9,12 15,12 15,22"/>
      </svg>
      <span>首頁</span>
    </router-link>

    <!-- 商品館 -->
    <router-link to="/products" class="mobile-nav-item" :class="{ active: isActive('/products') }">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
        <line x1="8" y1="21" x2="16" y2="21"/>
        <line x1="12" y1="17" x2="12" y2="21"/>
      </svg>
      <span>商品</span>
    </router-link>

    <!-- 品牌館 -->
    <router-link to="/brands" class="mobile-nav-item" :class="{ active: isActive('/brands') }">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
      <span>品牌</span>
    </router-link>

    <!-- 社員專區 -->
    <router-link 
      :to="memberLink" 
      class="mobile-nav-item" 
      :class="{ active: isActive('/member') || (isActive('/products') && isMemberArea) }"
      @click="handleMemberClick"
    >
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
        <circle cx="9" cy="7" r="4"/>
        <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
      </svg>
      <span>社員</span>
    </router-link>

    <!-- 個人中心/登入 -->
    <router-link 
      v-if="isUser" 
      to="/account" 
      class="mobile-nav-item" 
      :class="{ active: isActive('/account') }"
    >
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
        <circle cx="12" cy="7" r="4"/>
      </svg>
      <span>我的</span>
    </router-link>

    <router-link 
      v-else 
      to="/login" 
      class="mobile-nav-item" 
      :class="{ active: isActive('/login') }"
    >
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
        <polyline points="10,17 15,12 10,7"/>
        <line x1="15" y1="12" x2="3" y2="12"/>
      </svg>
      <span>登入</span>
    </router-link>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'

// Pinia store
const handelPinia = piniaStores()
const { isUser, clientData } = storeToRefs(handelPinia)

// Router
const route = useRoute()
const router = useRouter()

// 檢查當前路由是否為社員專區
const isMemberArea = computed(() => {
  return route.query.sort === '社員專區'
})

// 社員專區連結
const memberLink = computed(() => {
  if (isUser.value) {
    return {
      path: '/products',
      query: { sort: '社員專區' }
    }
  }
  return '/login'
})

// 檢查路由是否活躍
const isActive = (path) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 處理社員專區點擊
const handleMemberClick = (event) => {
  if (!isUser.value) {
    event.preventDefault()
    router.push('/login')
    message.warning('請先登入以訪問社員專區')
    return
  }
  
  // 如果已登入，導向社員專區
  router.push({
    path: '/products',
    query: { sort: '社員專區' }
  })
}
</script>

<style scoped>
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background-color: #ffffff;
  border-top: 1px solid #e5e7eb;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  /* 支援iOS安全區域 */
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  text-decoration: none;
  color: #6b7280;
  transition: color 0.2s ease, transform 0.1s ease;
  min-width: 60px;
  position: relative;
}

.mobile-nav-item:active {
  transform: scale(0.95);
}

.mobile-nav-item.active {
  color: #1f2937;
}

.mobile-nav-item.active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #1f2937;
  border-radius: 50%;
}

.mobile-nav-item svg {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  transition: transform 0.2s ease;
}

.mobile-nav-item.active svg {
  transform: scale(1.1);
}

.mobile-nav-item span {
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  transition: font-weight 0.2s ease;
}

.mobile-nav-item.active span {
  font-weight: 600;
}

/* 響應式調整 */
@media (max-width: 480px) {
  .mobile-bottom-nav {
    height: 70px;
    padding: 6px 0;
    padding-bottom: calc(6px + env(safe-area-inset-bottom));
  }
  
  .mobile-nav-item {
    padding: 6px 8px;
    min-width: 50px;
  }
  
  .mobile-nav-item svg {
    width: 20px;
    height: 20px;
    margin-bottom: 3px;
  }
  
  .mobile-nav-item span {
    font-size: 11px;
  }
}

/* 超寬螢幕適配 */
@media (min-width: 768px) {
  .mobile-bottom-nav {
    max-width: 768px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 12px 12px 0 0;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
  }
}

/* 橫屏模式調整 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-bottom-nav {
    height: 60px;
    padding: 4px 0;
    padding-bottom: calc(4px + env(safe-area-inset-bottom));
  }
  
  .mobile-nav-item {
    padding: 4px 8px;
  }
  
  .mobile-nav-item svg {
    width: 18px;
    height: 18px;
    margin-bottom: 2px;
  }
  
  .mobile-nav-item span {
    font-size: 10px;
  }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
  .mobile-bottom-nav {
    background-color: #1f2937;
    border-top-color: #374151;
  }
  
  .mobile-nav-item {
    color: #9ca3af;
  }
  
  .mobile-nav-item.active {
    color: #ffffff;
  }
  
  .mobile-nav-item.active::before {
    background-color: #ffffff;
  }
}
</style> 