<template>
  <!-- 背景遮罩 -->
  <div 
    v-if="isOpen"
    class="mobile-drawer-overlay"
    @click="closeDrawer"
    :class="{ 'mobile-drawer-overlay-open': isOpen }"
  ></div>

  <!-- 購物車抽屜 -->
  <div 
    class="mobile-cart-drawer"
    :class="{ 'mobile-drawer-open': isOpen }"
  >
    <!-- 抽屜頭部 -->
    <div class="mobile-drawer-header">
      <h3 class="mobile-drawer-title">購物車</h3>
      <button @click="closeDrawer" class="mobile-drawer-close">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>

    <!-- 購物車內容 -->
    <div class="mobile-drawer-content">
      <!-- 空購物車狀態 -->
      <div v-if="carts.length === 0" class="mobile-empty-cart">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="mobile-empty-cart-icon">
          <circle cx="9" cy="21" r="1"/>
          <circle cx="20" cy="21" r="1"/>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
        </svg>
        <p class="mobile-empty-cart-text">購物車是空的</p>
        <button @click="goToProducts" class="mobile-button mobile-button-primary mobile-button-small">
          去購物
        </button>
      </div>

      <!-- 購物車商品列表 -->
      <div v-else class="mobile-cart-items">
        <div 
          v-for="item in carts" 
          :key="item.id || item.productId"
          class="mobile-cart-item"
        >
          <!-- 商品圖片 -->
          <div class="mobile-cart-item-image">
            <img 
              :src="getProductImage(item)"
              :alt="item.name || item.productName"
              class="mobile-cart-image"
              @error="handleImageError"
            />
          </div>

          <!-- 商品信息 -->
          <div class="mobile-cart-item-info">
            <h4 class="mobile-cart-item-title">{{ item.name || item.productName }}</h4>
            <p v-if="item.specifications" class="mobile-cart-item-specs">{{ item.specifications }}</p>
            <div class="mobile-cart-item-price">
              <span class="mobile-price-current">NT$ {{ formatPrice(item.price || item.productPrice) }}</span>
              <span v-if="item.originalPrice && item.originalPrice > item.price" class="mobile-price-original">
                NT$ {{ formatPrice(item.originalPrice) }}
              </span>
            </div>

            <!-- 數量控制 -->
            <div class="mobile-quantity-controls">
              <button 
                @click="decreaseQuantity(item)"
                class="mobile-quantity-button"
                :disabled="item.quantity <= 1"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
              </button>
              <input 
                v-model.number="item.quantity"
                @change="updateQuantity(item)"
                type="number"
                min="1"
                class="mobile-quantity-input"
              />
              <button 
                @click="increaseQuantity(item)"
                class="mobile-quantity-button"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="12" y1="5" x2="12" y2="19"/>
                  <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- 刪除按鈕 -->
          <button @click="removeItem(item)" class="mobile-cart-item-remove">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="3,6 5,6 21,6"/>
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 結帳區域 -->
    <div v-if="carts.length > 0" class="mobile-cart-footer">
      <div class="mobile-cart-summary">
        <div class="mobile-cart-total">
          <span class="mobile-total-label">總計：</span>
          <span class="mobile-total-amount">NT$ {{ formatPrice(totalAmount) }}</span>
        </div>
        <p class="mobile-cart-count">共 {{ totalItems }} 件商品</p>
      </div>
      
      <button 
        @click="goToCheckout"
        class="mobile-button mobile-button-primary mobile-button-full mobile-checkout-button"
        :disabled="carts.length === 0"
      >
        前往結帳
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'
import { getImageUrl } from '@/config/apiConfig'

// Props
defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// Pinia store
const handelPinia = piniaStores()
const { carts, isUser } = storeToRefs(handelPinia)

// Router
const router = useRouter()

// 計算總金額
const totalAmount = computed(() => {
  return carts.value.reduce((total, item) => {
    const price = item.price || item.productPrice || 0
    const quantity = item.quantity || 1
    return total + (price * quantity)
  }, 0)
})

// 計算商品總數
const totalItems = computed(() => {
  return carts.value.reduce((total, item) => total + (item.quantity || 1), 0)
})

// 關閉抽屜
const closeDrawer = () => {
  emit('close')
}

// 格式化價格
const formatPrice = (price) => {
  if (!price) return '0'
  return parseInt(price).toLocaleString()
}

// 獲取商品圖片
const getProductImage = (item) => {
  if (item.image) {
    return getImageUrl(item.image)
  }
  if (item.productImage) {
    return getImageUrl(item.productImage)
  }
  return '/no-image.jpg'
}

// 圖片載入錯誤處理
const handleImageError = (event) => {
  event.target.src = '/no-image.jpg'
}

// 增加數量
const increaseQuantity = (item) => {
  item.quantity = (item.quantity || 1) + 1
  updateQuantity(item)
}

// 減少數量
const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    item.quantity = item.quantity - 1
    updateQuantity(item)
  }
}

// 更新數量
const updateQuantity = (item) => {
  // 確保數量不小於1
  if (item.quantity < 1) {
    item.quantity = 1
  }
  
  // 更新購物車
  handelPinia.updateCartItem(item)
}

// 移除商品
const removeItem = (item) => {
  handelPinia.removeFromCart(item.id || item.productId)
  message.success('已從購物車移除')
}

// 前往商品頁面
const goToProducts = () => {
  closeDrawer()
  router.push('/products')
}

// 前往結帳
const goToCheckout = () => {
  if (!isUser.value) {
    closeDrawer()
    router.push('/login')
    message.warning('請先登入以進行結帳')
    return
  }
  
  closeDrawer()
  router.push('/check')
}

// 監聽購物車變化
watch(carts, (newCarts) => {
  if (newCarts.length === 0) {
    // 購物車為空時可以選擇自動關閉抽屜
    // closeDrawer()
  }
}, { deep: true })
</script>

<style scoped>
/* 背景遮罩 */
.mobile-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mobile-drawer-overlay-open {
  opacity: 1;
  pointer-events: auto;
}

/* 購物車抽屜 */
.mobile-cart-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 400px;
  background-color: #ffffff;
  z-index: 95;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-drawer-open {
  transform: translateX(0);
}

/* 抽屜頭部 */
.mobile-drawer-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.mobile-drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.mobile-drawer-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.mobile-drawer-close:active {
  background-color: #f3f4f6;
}

.mobile-drawer-close svg {
  width: 18px;
  height: 18px;
}

/* 抽屜內容 */
.mobile-drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  -webkit-overflow-scrolling: touch;
}

/* 空購物車狀態 */
.mobile-empty-cart {
  text-align: center;
  padding: 60px 20px;
}

.mobile-empty-cart-icon {
  width: 64px;
  height: 64px;
  color: #d1d5db;
  margin: 0 auto 16px;
}

.mobile-empty-cart-text {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 24px;
}

/* 購物車商品項目 */
.mobile-cart-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobile-cart-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  gap: 12px;
  position: relative;
}

.mobile-cart-item-image {
  flex-shrink: 0;
}

.mobile-cart-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  background-color: #f3f4f6;
}

.mobile-cart-item-info {
  flex: 1;
  min-width: 0;
}

.mobile-cart-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mobile-cart-item-specs {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.mobile-cart-item-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.mobile-price-current {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.mobile-price-original {
  font-size: 14px;
  color: #9ca3af;
  text-decoration: line-through;
}

/* 數量控制 */
.mobile-quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-quantity-button {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #374151;
  transition: background-color 0.2s ease;
}

.mobile-quantity-button:active {
  background-color: #f3f4f6;
}

.mobile-quantity-button:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.mobile-quantity-button svg {
  width: 14px;
  height: 14px;
}

.mobile-quantity-input {
  width: 50px;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px;
  font-size: 14px;
}

/* 移除按鈕 */
.mobile-cart-item-remove {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.mobile-cart-item-remove:active {
  background-color: #fee2e2;
}

.mobile-cart-item-remove svg {
  width: 16px;
  height: 16px;
}

/* 結帳區域 */
.mobile-cart-footer {
  padding: 20px 16px;
  border-top: 1px solid #e5e7eb;
  background-color: #ffffff;
  flex-shrink: 0;
}

.mobile-cart-summary {
  margin-bottom: 16px;
}

.mobile-cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.mobile-total-label {
  font-size: 16px;
  color: #374151;
}

.mobile-total-amount {
  font-size: 20px;
  font-weight: 700;
  color: #dc2626;
}

.mobile-cart-count {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.mobile-checkout-button {
  font-size: 16px;
  font-weight: 600;
  min-height: 48px;
}

/* 響應式調整 */
@media (max-width: 480px) {
  .mobile-cart-drawer {
    width: 100%;
    max-width: none;
  }
}

/* 橫屏模式調整 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-drawer-header {
    padding: 12px 16px;
  }
  
  .mobile-drawer-content {
    padding: 12px 16px;
  }
  
  .mobile-cart-footer {
    padding: 12px 16px;
  }
}
</style> 