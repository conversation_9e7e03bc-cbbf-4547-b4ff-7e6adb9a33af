<template>
  <!-- 背景遮罩 -->
  <div 
    v-if="isOpen"
    class="mobile-drawer-overlay"
    @click="closeDrawer"
    :class="{ 'mobile-drawer-overlay-open': isOpen }"
  ></div>

  <!-- 選單抽屜 -->
  <div 
    class="mobile-menu-drawer"
    :class="{ 'mobile-drawer-open': isOpen }"
  >
    <!-- 抽屜頭部 -->
    <div class="mobile-drawer-header">
      <!-- 用戶信息區域 -->
      <div v-if="isUser" class="mobile-user-info">
        <div class="mobile-user-avatar">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
        </div>
        <div class="mobile-user-details">
          <h3 class="mobile-user-name">{{ clientData.name || clientData.email || '用戶' }}</h3>
          <p class="mobile-user-email">{{ clientData.email }}</p>
        </div>
      </div>

      <!-- 未登入狀態 -->
      <div v-else class="mobile-guest-info">
        <div class="mobile-guest-avatar">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
        </div>
        <div class="mobile-guest-details">
          <h3 class="mobile-guest-name">歡迎光臨</h3>
          <router-link to="/login" @click="closeDrawer" class="mobile-login-link">
            登入/註冊
          </router-link>
        </div>
      </div>

      <button @click="closeDrawer" class="mobile-drawer-close">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>

    <!-- 選單內容 -->
    <div class="mobile-drawer-content">
      <!-- 主要導航 -->
      <div class="mobile-nav-section">
        <h4 class="mobile-section-title">主要功能</h4>
        <div class="mobile-nav-items">
          <router-link to="/" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
              <polyline points="9,22 9,12 15,12 15,22"/>
            </svg>
            <span>首頁</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <router-link to="/products" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
              <line x1="8" y1="21" x2="16" y2="21"/>
              <line x1="12" y1="17" x2="12" y2="21"/>
            </svg>
            <span>商品館</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <router-link to="/brands" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>品牌館</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <button @click="handleMemberClick" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <span>社員專區</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 商品分類 -->
      <div v-if="classSet.length > 0" class="mobile-nav-section">
        <h4 class="mobile-section-title">商品分類</h4>
        <div class="mobile-category-tree">
          <div 
            v-for="category in filteredCategories"
            :key="category.value"
            class="mobile-category-item"
          >
            <button 
              @click="toggleCategory(category)"
              class="mobile-category-header"
              :class="{ 'mobile-category-expanded': expandedCategories.includes(category.value) }"
            >
              <span class="mobile-category-name">{{ category.label }}</span>
              <svg 
                v-if="category.children && category.children.length > 0"
                class="mobile-category-toggle"
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor"
              >
                <polyline points="6,9 12,15 18,9"/>
              </svg>
            </button>

            <!-- 子分類 -->
            <div 
              v-if="category.children && category.children.length > 0 && expandedCategories.includes(category.value)"
              class="mobile-subcategories"
            >
              <router-link
                v-for="subcategory in category.children"
                :key="subcategory.value"
                :to="{ path: '/products', query: { sort: subcategory.value } }"
                @click="closeDrawer"
                class="mobile-subcategory-link"
              >
                {{ subcategory.label }}
              </router-link>
            </div>

            <!-- 無子分類的分類項目 -->
            <router-link
              v-if="!category.children || category.children.length === 0"
              :to="{ path: '/products', query: { sort: category.value } }"
              @click="closeDrawer"
              class="mobile-category-link"
            >
              {{ category.label }}
            </router-link>
          </div>
        </div>
      </div>

      <!-- 用戶功能 -->
      <div v-if="isUser" class="mobile-nav-section">
        <h4 class="mobile-section-title">個人功能</h4>
        <div class="mobile-nav-items">
          <router-link to="/account" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            <span>個人中心</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <router-link 
            :to="{ path: '/account', query: { tab: 'order' } }" 
            @click="closeDrawer" 
            class="mobile-nav-link"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            <span>我的訂單</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <button @click="logout" class="mobile-nav-link mobile-logout-button">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              <polyline points="16,17 21,12 16,7"/>
              <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
            <span>登出</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 信息頁面 -->
      <div class="mobile-nav-section">
        <h4 class="mobile-section-title">服務信息</h4>
        <div class="mobile-nav-items">
          <router-link to="/aboutUs" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="16" x2="12" y2="12"/>
              <line x1="12" y1="8" x2="12" y2="8"/>
            </svg>
            <span>關於我們</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <router-link to="/service" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
            <span>服務項目</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>

          <router-link to="/news" @click="closeDrawer" class="mobile-nav-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
            <span>最新消息</span>
            <svg class="mobile-nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'

// Props
defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// Pinia store
const handelPinia = piniaStores()
const { isUser, clientData, classSet } = storeToRefs(handelPinia)

// Router
const router = useRouter()

// 展開的分類
const expandedCategories = ref([])

// 過濾掉品牌館分類（因為有專門的品牌館頁面）
const filteredCategories = computed(() => {
  return classSet.value.filter(category => category.label !== '品牌館')
})

// 關閉抽屜
const closeDrawer = () => {
  emit('close')
}

// 切換分類展開/收合
const toggleCategory = (category) => {
  if (!category.children || category.children.length === 0) {
    // 如果沒有子分類，直接導航
    router.push({ path: '/products', query: { sort: category.value } })
    closeDrawer()
    return
  }

  const index = expandedCategories.value.indexOf(category.value)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(category.value)
  }
}

// 處理社員專區點擊
const handleMemberClick = () => {
  if (!isUser.value) {
    closeDrawer()
    router.push('/login')
    message.warning('請先登入以訪問社員專區')
    return
  }

  closeDrawer()
  router.push({
    path: '/products',
    query: { sort: '社員專區' }
  })
}

// 登出功能
const logout = () => {
  // 清除登入狀態
  localStorage.removeItem('isLogin')
  localStorage.removeItem('token')
  localStorage.removeItem('saved_token')
  localStorage.removeItem('token_expiry')

  // 更新 pinia 狀態
  handelPinia.logout()

  // 關閉抽屜
  closeDrawer()

  // 顯示登出成功訊息
  message.success('登出成功')

  // 重新載入頁面
  window.location.href = '/'
}
</script>

<style scoped>
/* 背景遮罩 */
.mobile-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mobile-drawer-overlay-open {
  opacity: 1;
  pointer-events: auto;
}

/* 選單抽屜 */
.mobile-menu-drawer {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  max-width: 320px;
  background-color: #ffffff;
  z-index: 95;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-drawer-open {
  transform: translateX(0);
}

/* 抽屜頭部 */
.mobile-drawer-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

/* 用戶信息 */
.mobile-user-info,
.mobile-guest-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.mobile-user-avatar,
.mobile-guest-avatar {
  width: 48px;
  height: 48px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mobile-user-avatar svg,
.mobile-guest-avatar svg {
  width: 24px;
  height: 24px;
  color: white;
}

.mobile-user-details,
.mobile-guest-details {
  flex: 1;
  min-width: 0;
}

.mobile-user-name,
.mobile-guest-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-user-email {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-login-link {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  display: inline-block;
  transition: background-color 0.2s ease;
}

.mobile-login-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-drawer-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.mobile-drawer-close:active {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-drawer-close svg {
  width: 18px;
  height: 18px;
}

/* 抽屜內容 */
.mobile-drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  -webkit-overflow-scrolling: touch;
}

/* 選單區段 */
.mobile-nav-section {
  margin-bottom: 24px;
}

.mobile-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 12px 0;
  padding: 0 16px;
}

.mobile-nav-items {
  display: flex;
  flex-direction: column;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 16px;
  text-decoration: none;
  color: #374151;
  transition: background-color 0.2s ease;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mobile-nav-link:hover,
.mobile-nav-link:active {
  background-color: #f3f4f6;
}

.mobile-nav-link svg:first-child {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  color: #6b7280;
  flex-shrink: 0;
}

.mobile-nav-link span {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
}

.mobile-nav-arrow {
  width: 16px;
  height: 16px;
  color: #9ca3af;
  flex-shrink: 0;
}

.mobile-logout-button {
  color: #ef4444;
}

.mobile-logout-button svg:first-child {
  color: #ef4444;
}

/* 分類樹 */
.mobile-category-tree {
  display: flex;
  flex-direction: column;
}

.mobile-category-item {
  border-bottom: 1px solid #f3f4f6;
}

.mobile-category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-category-header:hover,
.mobile-category-header:active {
  background-color: #f9fafb;
}

.mobile-category-name {
  font-size: 15px;
  font-weight: 500;
  color: #374151;
}

.mobile-category-toggle {
  width: 16px;
  height: 16px;
  color: #9ca3af;
  transition: transform 0.2s ease;
}

.mobile-category-expanded .mobile-category-toggle {
  transform: rotate(180deg);
}

.mobile-category-link {
  display: block;
  padding: 16px;
  text-decoration: none;
  color: #374151;
  font-size: 15px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.mobile-category-link:hover,
.mobile-category-link:active {
  background-color: #f9fafb;
}

/* 子分類 */
.mobile-subcategories {
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.mobile-subcategory-link {
  display: block;
  padding: 12px 16px 12px 32px;
  text-decoration: none;
  color: #6b7280;
  font-size: 14px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.mobile-subcategory-link:last-child {
  border-bottom: none;
}

.mobile-subcategory-link:hover,
.mobile-subcategory-link:active {
  background-color: #f3f4f6;
  color: #374151;
  padding-left: 36px;
}

/* 響應式調整 */
@media (max-width: 480px) {
  .mobile-menu-drawer {
    max-width: 280px;
  }
  
  .mobile-drawer-header {
    padding: 16px 12px;
  }
  
  .mobile-nav-link {
    padding: 14px 12px;
  }
  
  .mobile-section-title {
    padding: 0 12px;
  }
}

/* 橫屏模式調整 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-drawer-header {
    padding: 12px 16px;
  }
  
  .mobile-drawer-content {
    padding: 16px 0;
  }
  
  .mobile-nav-section {
    margin-bottom: 16px;
  }
}
</style> 