<template>
  <header class="mobile-header">
    <!-- Logo區域 -->
    <router-link to="/" class="mobile-header-logo-container">
      <img 
        :src="logoSrc" 
        alt="Logo" 
        class="mobile-header-logo"
        @error="handleLogoError"
      />
    </router-link>

    <!-- 搜尋區域 -->
    <div class="mobile-search-container">
      <div class="mobile-search-wrapper">
        <svg class="mobile-search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜尋商品..."
          class="mobile-search-input"
          @keyup.enter="handleSearch"
          @focus="showSearchSuggestions = true"
          @blur="hideSearchSuggestions"
        />
        <button 
          v-if="searchQuery"
          @click="clearSearch"
          class="mobile-search-clear"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
      
      <!-- 搜尋建議 -->
      <div 
        v-if="showSearchSuggestions && searchSuggestions.length > 0"
        class="mobile-search-suggestions"
      >
        <div 
          v-for="suggestion in searchSuggestions"
          :key="suggestion"
          class="mobile-search-suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <svg class="mobile-suggestion-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
          </svg>
          <span>{{ suggestion }}</span>
        </div>
      </div>
    </div>

    <!-- 右側功能按鈕 -->
    <div class="mobile-header-actions">
      <!-- 購物車按鈕 -->
      <button 
        @click="toggleCart"
        class="mobile-header-button mobile-cart-button"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="9" cy="21" r="1"/>
          <circle cx="20" cy="21" r="1"/>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
        </svg>
        <span v-if="cartItemsCount > 0" class="mobile-cart-badge">{{ cartItemsCount }}</span>
      </button>

      <!-- 用戶/選單按鈕 -->
      <button 
        @click="toggleMenu"
        class="mobile-header-button mobile-menu-button"
      >
        <svg v-if="!isUser" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
        <svg v-else viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="3" y1="6" x2="21" y2="6"/>
          <line x1="3" y1="12" x2="21" y2="12"/>
          <line x1="3" y1="18" x2="21" y2="18"/>
        </svg>
      </button>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'

// Pinia store
const handelPinia = piniaStores()
const { isUser, carts, clientData } = storeToRefs(handelPinia)

// Router
const router = useRouter()
const route = useRoute()

// Logo相關  
const logoSrc = ref('/favicon.ico')

// 搜尋相關
const searchQuery = ref('')
const showSearchSuggestions = ref(false)
const searchSuggestions = ref([])

// 事件發送器
const emit = defineEmits(['toggle-cart', 'toggle-menu'])

// 計算購物車商品數量
const cartItemsCount = computed(() => {
  return carts.value.reduce((total, item) => total + (item.quantity || 1), 0)
})

// Logo錯誤處理
const handleLogoError = () => {
  logoSrc.value = '/favicon.ico' // 備用logo
}

// 搜尋處理
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  // 保存搜尋歷史
  saveSearchHistory(searchQuery.value)
  
  // 執行搜尋
  if (route.path === '/products') {
    router.replace({
      path: '/products',
      query: { keyWord: searchQuery.value }
    })
  } else {
    router.push({
      path: '/products',
      query: { keyWord: searchQuery.value }
    })
  }
  
  // 隱藏搜尋建議
  showSearchSuggestions.value = false
}

// 清除搜尋
const clearSearch = () => {
  searchQuery.value = ''
  showSearchSuggestions.value = false
}

// 選擇搜尋建議
const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion
  handleSearch()
}

// 隱藏搜尋建議（延遲執行，允許點擊建議項目）
const hideSearchSuggestions = () => {
  setTimeout(() => {
    showSearchSuggestions.value = false
  }, 200)
}

// 搜尋歷史管理
const saveSearchHistory = (query) => {
  const history = getSearchHistory()
  const filteredHistory = history.filter(item => item !== query)
  const newHistory = [query, ...filteredHistory].slice(0, 5) // 保留最近5條
  localStorage.setItem('mobile_search_history', JSON.stringify(newHistory))
}

const getSearchHistory = () => {
  try {
    return JSON.parse(localStorage.getItem('mobile_search_history') || '[]')
  } catch {
    return []
  }
}

// 監聽搜尋輸入，顯示搜尋建議
watch(searchQuery, (newValue) => {
  if (newValue.trim()) {
    // 這裡可以添加搜尋建議的API調用
    // 目前使用搜尋歷史作為建議
    const history = getSearchHistory()
    searchSuggestions.value = history.filter(item => 
      item.toLowerCase().includes(newValue.toLowerCase())
    )
  } else {
    searchSuggestions.value = getSearchHistory().slice(0, 3)
  }
})

// 切換購物車
const toggleCart = () => {
  emit('toggle-cart')
}

// 切換選單
const toggleMenu = () => {
  emit('toggle-menu')
}

// 初始化搜尋建議
onMounted(() => {
  searchSuggestions.value = getSearchHistory().slice(0, 3)
})

// 監聽路由變化，清除搜尋框焦點
watch(() => route.path, () => {
  showSearchSuggestions.value = false
})
</script>

<style scoped>
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  z-index: 50;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  gap: 12px;
}

/* Logo樣式 */
.mobile-header-logo-container {
  flex-shrink: 0;
}

.mobile-header-logo {
  height: 36px;
  max-width: 80px;
  object-fit: contain;
}

/* 搜尋區域樣式 */
.mobile-search-container {
  flex: 1;
  position: relative;
  max-width: 300px;
}

.mobile-search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  border-radius: 20px;
  padding: 0 12px;
  height: 36px;
}

.mobile-search-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  margin-right: 8px;
  flex-shrink: 0;
}

.mobile-search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #374151;
  outline: none;
  placeholder-color: #9ca3af;
}

.mobile-search-input::placeholder {
  color: #9ca3af;
}

.mobile-search-clear {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  margin-left: 4px;
}

.mobile-search-clear svg {
  width: 14px;
  height: 14px;
}

/* 搜尋建議樣式 */
.mobile-search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.mobile-search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-search-suggestion-item:last-child {
  border-bottom: none;
}

.mobile-search-suggestion-item:hover {
  background-color: #f9fafb;
}

.mobile-suggestion-icon {
  width: 14px;
  height: 14px;
  color: #9ca3af;
  margin-right: 8px;
  flex-shrink: 0;
}

.mobile-search-suggestion-item span {
  font-size: 14px;
  color: #374151;
}

/* 右側功能按鈕樣式 */
.mobile-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.mobile-header-button {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  color: #374151;
  position: relative;
}

.mobile-header-button:active {
  background-color: #f3f4f6;
}

.mobile-header-button svg {
  width: 20px;
  height: 20px;
}

/* 購物車徽章 */
.mobile-cart-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background-color: #dc2626;
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 響應式調整 */
@media (max-width: 480px) {
  .mobile-header {
    padding: 0 12px;
    gap: 8px;
  }
  
  .mobile-header-logo {
    max-width: 60px;
  }
  
  .mobile-search-wrapper {
    height: 32px;
    padding: 0 10px;
  }
  
  .mobile-search-input {
    font-size: 13px;
  }
  
  .mobile-header-button {
    width: 32px;
    height: 32px;
  }
  
  .mobile-header-button svg {
    width: 18px;
    height: 18px;
  }
}
</style> 