<template>
  <component :is="currentComponent" v-bind="$attrs" />
</template>

<script setup>
import { computed, defineAsyncComponent } from 'vue'
import { useDeviceDetection } from '@/utils/deviceUtils'

// Props
const props = defineProps({
  desktopComponent: {
    type: String,
    required: true
  },
  mobileComponent: {
    type: String,
    required: false,
    default: null
  }
})

// 設備檢測
const { deviceInfo } = useDeviceDetection()

// 動態載入組件
const currentComponent = computed(() => {
  // 如果是移動端且有移動端組件，使用移動端組件
  if (deviceInfo.isMobile && props.mobileComponent) {
    return defineAsyncComponent(() => import(`@/views/mobile/${props.mobileComponent}.vue`))
  }
  
  // 否則使用桌面端組件
  return defineAsyncComponent(() => import(`@/views/${props.desktopComponent}.vue`))
})
</script>

<style scoped>
/* 無需樣式，純邏輯組件 */
</style> 