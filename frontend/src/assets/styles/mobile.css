/* ==========================================
   移動端專用樣式 - Mobile-First Design
   ========================================== */

/* 基礎重置和設定 */
.mobile-app {
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
  background-color: #ffffff;
}

/* 移動端視窗設定 */
.mobile-viewport {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* 移動端主內容區 */
.mobile-main-content {
  flex: 1;
  width: 100%;
  padding-top: 60px; /* 為固定頭部留出空間 */
  padding-bottom: 80px; /* 為固定底部留出空間 */
  min-height: calc(100vh - 140px);
  overflow-x: hidden;
  overflow-y: auto; /* 允許垂直捲動 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑捲動 */
}

/* ==========================================
   移動端頭部樣式
   ========================================== */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-header-logo {
  height: 36px;
  max-width: 120px;
  object-fit: contain;
}

.mobile-header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-header-button {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  color: #374151;
}

.mobile-header-button:active {
  background-color: #f3f4f6;
}

.mobile-header-button svg {
  width: 20px;
  height: 20px;
}

/* ==========================================
   移動端底部導航樣式
   ========================================== */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background-color: #ffffff;
  border-top: 1px solid #e5e7eb;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  text-decoration: none;
  color: #6b7280;
  transition: color 0.2s ease;
  min-width: 60px;
}

.mobile-nav-item.active {
  color: #1f2937;
}

.mobile-nav-item svg {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.mobile-nav-item span {
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
}

/* ==========================================
   移動端容器和佈局
   ========================================== */
.mobile-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 16px;
}

.mobile-container-tight {
  padding: 0 12px;
}

.mobile-container-loose {
  padding: 0 20px;
}

/* 移動端網格系統 */
.mobile-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

.mobile-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.mobile-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* 移動端卡片 */
.mobile-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.mobile-card-content {
  padding: 16px;
}

/* ==========================================
   移動端圖片優化
   ========================================== */
.mobile-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

.mobile-image-contain {
  object-fit: contain;
}

.mobile-image-square {
  aspect-ratio: 1 / 1;
}

.mobile-image-landscape {
  aspect-ratio: 16 / 9;
}

.mobile-image-portrait {
  aspect-ratio: 3 / 4;
}

/* 圖片載入狀態 */
.mobile-image-loading {
  background-color: #f3f4f6;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ==========================================
   移動端文字樣式
   ========================================== */
.mobile-text-xs {
  font-size: 12px;
  line-height: 16px;
}

.mobile-text-sm {
  font-size: 14px;
  line-height: 20px;
}

.mobile-text-base {
  font-size: 16px;
  line-height: 24px;
}

.mobile-text-lg {
  font-size: 18px;
  line-height: 28px;
}

.mobile-text-xl {
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}

.mobile-text-2xl {
  font-size: 24px;
  line-height: 32px;
  font-weight: 700;
}

/* 文字顏色 */
.mobile-text-primary {
  color: #111827;
}

.mobile-text-secondary {
  color: #6b7280;
}

.mobile-text-muted {
  color: #9ca3af;
}

.mobile-text-accent {
  color: #dc2626;
}

/* ==========================================
   移動端按鈕樣式
   ========================================== */
.mobile-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px; /* 觸控友好的最小高度 */
  user-select: none;
}

.mobile-button-primary {
  background-color: #111827;
  color: #ffffff;
}

.mobile-button-primary:active {
  background-color: #1f2937;
  transform: scale(0.98);
}

.mobile-button-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.mobile-button-secondary:active {
  background-color: #e5e7eb;
  transform: scale(0.98);
}

.mobile-button-outline {
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.mobile-button-outline:active {
  background-color: #f9fafb;
  transform: scale(0.98);
}

.mobile-button-full {
  width: 100%;
}

.mobile-button-large {
  padding: 16px 32px;
  font-size: 18px;
  min-height: 56px;
}

.mobile-button-small {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 40px;
}

/* ==========================================
   移動端表單樣式
   ========================================== */
.mobile-form-group {
  margin-bottom: 20px;
}

.mobile-form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.mobile-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px; /* 防止iOS縮放 */
  background-color: #ffffff;
  transition: border-color 0.2s ease;
  min-height: 48px;
}

.mobile-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.mobile-form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* ==========================================
   移動端模態框和彈出層
   ========================================== */
.mobile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.mobile-modal {
  background-color: #ffffff;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.mobile-modal-header {
  padding: 20px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.mobile-modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.mobile-modal-body {
  padding: 20px 24px 24px;
}

/* ==========================================
   移動端側邊欄/抽屜
   ========================================== */
.mobile-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  transition: opacity 0.3s ease;
}

.mobile-drawer {
  position: fixed;
  top: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 95;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.mobile-drawer-left {
  left: 0;
  width: 280px;
  transform: translateX(-100%);
}

.mobile-drawer-right {
  right: 0;
  width: 280px;
  transform: translateX(100%);
}

.mobile-drawer-open {
  transform: translateX(0);
}

.mobile-drawer-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-drawer-content {
  padding: 16px;
}

/* ==========================================
   移動端載入和狀態樣式
   ========================================== */
.mobile-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.mobile-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.mobile-empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.mobile-empty-state svg {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  color: #d1d5db;
}

/* ==========================================
   移動端工具類
   ========================================== */
.mobile-safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.mobile-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.mobile-safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.mobile-touch-manipulation {
  touch-action: manipulation;
}

.mobile-no-scroll {
  overflow: hidden;
}

.mobile-scroll-y {
  overflow-y: auto;
}

.mobile-hidden {
  display: none !important;
}

.mobile-visible {
  display: block !important;
}

/* ==========================================
   移動端響應式工具
   ========================================== */
@media (max-width: 480px) {
  .mobile-container {
    padding: 0 12px;
  }
  
  .mobile-button {
    font-size: 14px;
    padding: 10px 20px;
  }
  
  .mobile-text-base {
    font-size: 14px;
    line-height: 20px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .mobile-container {
    padding: 0 20px;
  }
}

/* ==========================================
   移動端購物車和商品樣式
   ========================================== */
.mobile-product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

.mobile-product-card {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.mobile-product-card:active {
  transform: scale(0.98);
}

.mobile-product-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  background-color: #f3f4f6;
}

.mobile-product-info {
  padding: 12px;
}

.mobile-product-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mobile-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.mobile-cart-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-cart-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 12px;
}

.mobile-cart-info {
  flex: 1;
}

.mobile-cart-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.mobile-cart-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.mobile-quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.mobile-quantity-button {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #374151;
}

.mobile-quantity-input {
  width: 60px;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px;
  font-size: 14px;
} 