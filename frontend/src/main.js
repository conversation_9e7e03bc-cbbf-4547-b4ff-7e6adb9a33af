import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import Antd, { message } from 'ant-design-vue'
import App from './App.vue'
import router from './router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-tw'

// 先引入 Ant Design 樣式，再引入自定義樣式，確保自定義樣式優先級更高
import 'ant-design-vue/dist/reset.css'
import './assets/main.css'
import './assets/css/rich-text.css'
import './assets/styles/rich-text.css' // 確保富文本樣式能覆蓋 Ant Design 樣式
import './assets/styles/highlights.css' // 添加 highlights 樣式，確保可以覆蓋 Ant Design 樣式
import './assets/styles/mobile.css' // 移動端專用樣式

// 設置dayjs為中文繁體
dayjs.locale('zh-tw')

const app = createApp(App)
const pinia = createPinia()

// 配置 Pinia 持久化插件
pinia.use(piniaPluginPersistedstate)

// 全局配置 antd message
message.config({
  top: `100px`,
  duration: 1.5,
  maxCount: 3,
});

// 使用插件
app.use(pinia)
app.use(router)
app.use(Antd)

// 掛載應用
app.mount('#app')