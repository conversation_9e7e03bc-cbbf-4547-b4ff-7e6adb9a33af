<template>
  <!-- 桌面版佈局 -->
  <div v-if="!deviceInfo.isMobile" class="desktop-app">
    <HeaderItem />
    <main class="main-content">
      <router-view />
    </main>
    <FooterItem />
  </div>

  <!-- 移動端佈局 -->
  <MobileApp v-else />
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import HeaderItem from '@/components/HeaderItem.vue'
import FooterItem from '@/components/FooterItem.vue'
import MobileApp from '@/MobileApp.vue'
import { useDeviceDetection } from '@/utils/deviceUtils'

// 初始化 Pinia store
const handelPinia = piniaStores()
const { classSet, isUser, carts, total } = storeToRefs(handelPinia)

// 設備檢測
const { deviceInfo, cleanup } = useDeviceDetection()

// 在應用啟動時預加載分類數據並檢查登入狀態
onMounted(async () => {
  console.log('App: 應用啟動，設備類型:', deviceInfo.type, '移動端:', deviceInfo.isMobile)
  
  try {
    // 直接載入分類數據，不需要先清空
    await handelPinia.getClassSet()
    console.log('App: 分類數據加載完成，共', classSet.value.length, '項')
    
    // 檢查用戶登入狀態
    const token = localStorage.getItem('token')
    const isLogin = localStorage.getItem('isLogin')
    
    // 如果用戶未登入但購物車有數據，則清空購物車
    if ((!token || !isLogin) && carts.value.length > 0) {
      console.log('App: 用戶未登入，清空購物車')
      handelPinia.clearCart()
    } else {
      // 初始化用戶狀態
      handelPinia.initUser(true)
    }
  } catch (error) {
    console.error('App: 預加載分類數據失敗', error)
  }
})

// 清理設備檢測監聽器
onUnmounted(() => {
  cleanup()
})
</script>

<style>
/* 全局樣式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
  width: 100%;
  height: 100%;
  overflow-x: hidden; /* 防止水平滾動 */
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden; /* 防止水平滾動 */
}

/* 主內容區域樣式 */
.main-content {
  padding-top: 80px; /* 固定頂部間距，適應 header 高度 */
  flex: 1;
  width: 100%;
  position: relative;
  z-index: 1; /* 確保內容在正確的層級 */
}

/* 確保下拉選單在所有頁面正確顯示 */
.dropdown-menu {
  z-index: 1001; /* 設置比header更高的z-index */
}

/* 全局組件樣式優先級 */
header {
  z-index: 1000 !important; /* 確保header永遠在最頂層 */
}

/* 禁止頁面元素覆蓋固定的header */
.fixed, .absolute {
  z-index: auto;
}
</style>