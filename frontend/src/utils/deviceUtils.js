import { ref, reactive } from 'vue'

// 設備檢測工具
class DeviceDetector {
  constructor() {
    this.userAgent = navigator.userAgent
    this.screenWidth = window.innerWidth
    this.screenHeight = window.innerHeight
    this.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  // 檢測是否為移動設備
  isMobile() {
    // 檢查螢幕寬度 (小於768px視為移動設備)
    if (this.screenWidth < 768) return true

    // 檢查User Agent是否包含移動設備標識
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
    if (mobileRegex.test(this.userAgent)) return true

    // 檢查是否為觸控設備且螢幕較小
    if (this.isTouchDevice && this.screenWidth < 1024) return true

    return false
  }

  // 檢測是否為平板設備
  isTablet() {
    // 平板通常是768px - 1024px之間的觸控設備
    if (this.isTouchDevice && this.screenWidth >= 768 && this.screenWidth <= 1024) {
      return true
    }

    // iPad特殊檢測
    if (/iPad/i.test(this.userAgent)) return true

    // Android平板檢測（通常不含"Mobile"字樣）
    if (/Android/i.test(this.userAgent) && !/Mobile/i.test(this.userAgent)) {
      return true
    }

    return false
  }

  // 檢測是否為手機
  isPhone() {
    // 檢查螢幕寬度 (小於768px且不是平板)
    if (this.screenWidth < 768 && !this.isTablet()) return true

    // iPhone檢測
    if (/iPhone/i.test(this.userAgent)) return true

    // Android手機檢測（包含"Mobile"字樣）
    if (/Android/i.test(this.userAgent) && /Mobile/i.test(this.userAgent)) {
      return true
    }

    return false
  }

  // 檢測是否為桌面設備
  isDesktop() {
    return !this.isMobile() && !this.isTablet()
  }

  // 獲取設備類型字符串
  getDeviceType() {
    if (this.isPhone()) return 'phone'
    if (this.isTablet()) return 'tablet'
    if (this.isDesktop()) return 'desktop'
    return 'unknown'
  }

  // 檢測是否需要使用移動端界面
  shouldUseMobileUI() {
    // 根據需求，10吋以下的設備使用移動端界面
    const tenInchWidthThreshold = 1024 // 約等於10吋平板的寬度
    
    return this.screenWidth < tenInchWidthThreshold || 
           this.isPhone() || 
           (this.isTablet() && this.screenWidth < tenInchWidthThreshold)
  }

  // 響應式更新設備信息
  updateScreenSize() {
    this.screenWidth = window.innerWidth
    this.screenHeight = window.innerHeight
  }
}

// 創建全局設備檢測實例
const deviceDetector = new DeviceDetector()

// Vue 3 組合式API響應式設備狀態
export function useDeviceDetection() {
  const deviceInfo = reactive({
    type: deviceDetector.getDeviceType(),
    isMobile: deviceDetector.shouldUseMobileUI(),
    isDesktop: deviceDetector.isDesktop(),
    screenWidth: deviceDetector.screenWidth,
    screenHeight: deviceDetector.screenHeight,
    isTouchDevice: deviceDetector.isTouchDevice
  })

  // 監聽窗口大小變化
  const updateDeviceInfo = () => {
    deviceDetector.updateScreenSize()
    deviceInfo.type = deviceDetector.getDeviceType()
    deviceInfo.isMobile = deviceDetector.shouldUseMobileUI()
    deviceInfo.isDesktop = deviceDetector.isDesktop()
    deviceInfo.screenWidth = deviceDetector.screenWidth
    deviceInfo.screenHeight = deviceDetector.screenHeight
  }

  // 監聽窗口變化事件
  window.addEventListener('resize', updateDeviceInfo)
  window.addEventListener('orientationchange', updateDeviceInfo)

  return {
    deviceInfo,
    updateDeviceInfo,
    cleanup: () => {
      window.removeEventListener('resize', updateDeviceInfo)
      window.removeEventListener('orientationchange', updateDeviceInfo)
    }
  }
}

// 簡單的設備檢測函數（非響應式）
export function detectDevice() {
  return {
    detector: deviceDetector,
    isMobile: deviceDetector.shouldUseMobileUI(),
    isDesktop: deviceDetector.isDesktop(),
    type: deviceDetector.getDeviceType()
  }
}

export default DeviceDetector 