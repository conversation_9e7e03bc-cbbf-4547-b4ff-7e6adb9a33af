<template>
  <div class="mobile-app">
    <!-- 移動端頭部 -->
    <MobileHeaderItem 
      @toggle-cart="toggleCart"
      @toggle-menu="toggleMenu"
    />
    
    <!-- 主內容區域 -->
    <main class="mobile-main-content">
      <component :is="currentMobileView" />
    </main>
    
    <!-- 移動端底部導航 -->
    <MobileBottomNav />
    
    <!-- 移動端購物車彈出層 -->
    <MobileCartDrawer 
      :isOpen="cartOpen"
      @close="closeCart"
    />
    
    <!-- 移動端選單抽屜 -->
    <MobileMenuDrawer 
      :isOpen="menuOpen"
      @close="closeMenu"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import MobileHeaderItem from '@/components/mobile/MobileHeaderItem.vue'
import MobileBottomNav from '@/components/mobile/MobileBottomNav.vue'
import MobileCartDrawer from '@/components/mobile/MobileCartDrawer.vue'
import MobileMenuDrawer from '@/components/mobile/MobileMenuDrawer.vue'

// 移動端視圖組件
import MobileHomeView from '@/views/mobile/MobileHomeView.vue'

// 初始化 Pinia store
const handelPinia = piniaStores()
const { classSet, isUser, carts, total } = storeToRefs(handelPinia)

// 路由
const route = useRoute()

// 移動端視圖映射
const mobileViewMap = {
  '/': MobileHomeView,
  // 可以在這裡添加更多移動端專用視圖
  // '/products': MobileProductsView,
  // '/product': MobileProductInfoView,
  // '/login': MobileLoginView,
  // '/account': MobileAccountView,
  // ...
}

// 計算當前移動端視圖
const currentMobileView = computed(() => {
  const mobilePath = route.path

  // 檢查是否有專用的移動端視圖
  if (mobileViewMap[mobilePath]) {
    return mobileViewMap[mobilePath]
  }

  // 如果沒有專用移動端視圖，使用 defineAsyncComponent 動態導入對應的桌面端視圖
  // 這樣可以確保所有頁面都能正常顯示，即使沒有專門的移動端版本
  return defineAsyncComponent({
    loader: () => {
      // 根據路徑決定要導入的組件
      let componentPath = ''

      if (mobilePath === '/') {
        componentPath = '@/views/HomeView.vue'
      } else if (mobilePath === '/products') {
        componentPath = '@/views/ProductsView.vue'
      } else if (mobilePath === '/product') {
        componentPath = '@/views/ProductInfo.vue'
      } else if (mobilePath === '/login') {
        componentPath = '@/views/LoginView.vue'
      } else if (mobilePath === '/account') {
        componentPath = '@/views/AccountView.vue'
      } else if (mobilePath === '/check') {
        componentPath = '@/views/CheckView.vue'
      } else if (mobilePath === '/brands') {
        componentPath = '@/views/BrandsView.vue'
      } else if (mobilePath === '/news') {
        componentPath = '@/views/NewsView.vue'
      } else if (mobilePath === '/service') {
        componentPath = '@/views/ServiceView.vue'
      } else if (mobilePath === '/aboutUs') {
        componentPath = '@/views/inform/AboutView.vue'
      } else if (mobilePath === '/shoppingNotice') {
        componentPath = '@/views/inform/ShoppingNotice.vue'
      } else if (mobilePath === '/transportNotice') {
        componentPath = '@/views/inform/TransportNotice.vue'
      } else if (mobilePath === '/privacy') {
        componentPath = '@/views/inform/PrivacyView.vue'
      } else if (mobilePath.startsWith('/brand/')) {
        componentPath = '@/views/BrandDetailView.vue'
      } else if (mobilePath === '/brands/all') {
        componentPath = '@/views/BrandDetailView.vue'
      } else {
        // 默認回退到首頁
        componentPath = '@/views/HomeView.vue'
      }

      return import(componentPath).catch((error) => {
        console.error('動態導入組件失敗:', error, '路徑:', componentPath)
        // 如果找不到對應的視圖，回退到首頁
        return { default: MobileHomeView }
      })
    },
    loadingComponent: {
      template: `
        <div class="mobile-loading">
          <div class="mobile-spinner"></div>
          <p class="mobile-loading-text">載入中...</p>
        </div>
      `
    },
    errorComponent: {
      template: `
        <div class="mobile-error">
          <p>頁面載入失敗，請重試</p>
        </div>
      `
    },
    delay: 200,
    timeout: 3000
  })
})

// 抽屜狀態管理
const cartOpen = ref(false)
const menuOpen = ref(false)

// 抽屜控制函數
const toggleCart = () => {
  cartOpen.value = !cartOpen.value
  // 關閉選單抽屜
  if (cartOpen.value) {
    menuOpen.value = false
  }
}

const closeCart = () => {
  cartOpen.value = false
}

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value
  // 關閉購物車抽屜
  if (menuOpen.value) {
    cartOpen.value = false
  }
}

const closeMenu = () => {
  menuOpen.value = false
}

// 設定移動端視窗
const setMobileViewport = () => {
  // 設定視窗meta標籤
  let viewport = document.querySelector('meta[name=viewport]')
  if (!viewport) {
    viewport = document.createElement('meta')
    viewport.name = 'viewport'
    document.head.appendChild(viewport)
  }

  // 移動端優化的視窗設定
  viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'

  // 添加移動端專用的body類別
  document.body.classList.add('mobile-body')

  // 移除阻止捲動的事件監聽器，允許正常的頁面捲動
  // 如果需要防止特定區域的滾動，應該在具體組件中處理
}

// 清理移動端設定
const cleanupMobileViewport = () => {
  document.body.classList.remove('mobile-body')
}

// 在應用啟動時預加載分類數據並檢查登入狀態
onMounted(async () => {
  // 設定移動端視窗
  setMobileViewport()
  
  try {
    // 預加載分類數據
    await handelPinia.getClassSet()
    
    // 檢查用戶登入狀態
    const token = localStorage.getItem('token')
    const isLogin = localStorage.getItem('isLogin')
    
    // 如果用戶未登入但購物車有數據，則清空購物車
    if ((!token || !isLogin) && carts.value.length > 0) {
      handelPinia.clearCart()
    } else {
      // 初始化用戶狀態
      handelPinia.initUser(true)
    }
  } catch (error) {
    console.error('MobileApp: 預加載分類數據失敗', error)
  }
})

onUnmounted(() => {
  cleanupMobileViewport()
})
</script>

<style scoped>
/* 移動端全局樣式覆蓋 */
.mobile-app {
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  background-color: #f9fafb;
}

/* 移動端主內容區域 */
.mobile-main-content {
  flex: 1;
  width: 100%;
  padding-top: 60px; /* 為固定頭部留出空間 */
  padding-bottom: 80px; /* 為固定底部留出空間 */
  min-height: calc(100vh - 140px);
  overflow-x: hidden;
  overflow-y: auto; /* 允許垂直捲動 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑捲動 */
}

/* 全局移動端body樣式 */
:global(.mobile-body) {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto; /* 允許垂直捲動 */
  -webkit-overflow-scrolling: touch;
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
  height: 100vh; /* 確保 body 有足夠的高度 */
}

/* 防止縮放和選擇 */
:global(.mobile-body *) {
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 允許表單輸入元素選擇 */
:global(.mobile-body input),
:global(.mobile-body textarea),
:global(.mobile-body select),
:global(.mobile-body [contenteditable]) {
  -webkit-user-select: auto;
}

/* 移動端安全區域 */
.mobile-app {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 載入和錯誤狀態樣式 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 200px;
}

.mobile-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.mobile-loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #6b7280;
}

.mobile-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 200px;
  color: #dc2626;
  text-align: center;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style> 