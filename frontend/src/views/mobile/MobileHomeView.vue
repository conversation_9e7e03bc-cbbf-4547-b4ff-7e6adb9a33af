<template>
  <div class="mobile-home">
    <!-- 輪播廣告區 -->
    <div class="mobile-banner-section">
      <div class="mobile-banner-container">
        <div class="mobile-banner-slide">
          <div class="mobile-banner-image"></div>
        </div>
      </div>
    </div>

    <!-- 快速導航區 -->
    <div class="mobile-quick-nav">
      <div class="mobile-container">
        <div class="mobile-nav-grid">
          <div class="mobile-nav-item">
            <div class="mobile-nav-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                <line x1="8" y1="21" x2="16" y2="21"/>
                <line x1="12" y1="17" x2="12" y2="21"/>
              </svg>
            </div>
            <span class="mobile-nav-text">全部商品</span>
          </div>

          <div class="mobile-nav-item">
            <div class="mobile-nav-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <span class="mobile-nav-text">品牌館</span>
          </div>

          <div class="mobile-nav-item">
            <div class="mobile-nav-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <span class="mobile-nav-text">社員專區</span>
          </div>

          <div class="mobile-nav-item">
            <div class="mobile-nav-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="16" x2="12" y2="12"/>
                <line x1="12" y1="8" x2="12" y2="8"/>
              </svg>
            </div>
            <span class="mobile-nav-text">關於我們</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品分類區 -->
    <div class="mobile-category-section">
      <div class="mobile-container">
        <h2 class="mobile-section-title">熱門分類</h2>
        <div class="mobile-category-grid">
          <div class="mobile-category-card">
            <div class="mobile-category-content">
              <h3 class="mobile-category-name">分類名稱</h3>
              <span class="mobile-category-count">0 商品</span>
            </div>
          </div>
          <div class="mobile-category-card">
            <div class="mobile-category-content">
              <h3 class="mobile-category-name">分類名稱</h3>
              <span class="mobile-category-count">0 商品</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 推薦商品區 -->
    <div class="mobile-featured-section">
      <div class="mobile-container">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">推薦商品</h2>
          <span class="mobile-view-all">
            查看全部
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </span>
        </div>
        <div class="mobile-product-grid">
          <div class="mobile-product-card">
            <div class="mobile-product-image-container">
              <div class="mobile-product-image"></div>
              <div class="mobile-product-badge mobile-badge-new">新品</div>
              <div class="mobile-product-badge mobile-badge-hot">熱銷</div>
            </div>
            <div class="mobile-product-info">
              <h3 class="mobile-product-title">商品名稱</h3>
              <div class="mobile-product-price">
                <span class="mobile-price-current">NT$ 0</span>
                <span class="mobile-price-original">NT$ 0</span>
              </div>
            </div>
          </div>
          <div class="mobile-product-card">
            <div class="mobile-product-image-container">
              <div class="mobile-product-image"></div>
            </div>
            <div class="mobile-product-info">
              <h3 class="mobile-product-title">商品名稱</h3>
              <div class="mobile-product-price">
                <span class="mobile-price-current">NT$ 0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 品牌區域 -->
    <div class="mobile-brands-section">
      <div class="mobile-container">
        <div class="mobile-section-header">
          <h2 class="mobile-section-title">熱門品牌</h2>
          <span class="mobile-view-all">
            查看全部
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </span>
        </div>
        <div class="mobile-brands-grid">
          <div class="mobile-brand-card">
            <div class="mobile-brand-image"></div>
            <span class="mobile-brand-name">品牌名稱</span>
          </div>
          <div class="mobile-brand-card">
            <div class="mobile-brand-image"></div>
            <span class="mobile-brand-name">品牌名稱</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 載入中狀態 -->
    <div class="mobile-loading">
      <div class="mobile-spinner"></div>
      <p class="mobile-loading-text">載入中...</p>
    </div>
  </div>
</template>

<style scoped>
.mobile-home {
  background-color: #f9fafb;
  min-height: 100vh;
}

/* 輪播廣告區 */
.mobile-banner-section {
  margin-bottom: 20px;
}

.mobile-banner-container {
  height: 200px;
  overflow: hidden;
}

.mobile-banner-swiper {
  height: 100%;
}

.mobile-banner-slide {
  height: 100%;
}

.mobile-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f3f4f6;
}

.mobile-banner-loading {
  height: 200px;
  padding: 16px;
}

.mobile-loading-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 2s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 快速導航區 */
.mobile-quick-nav {
  background-color: #ffffff;
  padding: 20px 0;
  margin-bottom: 20px;
}

.mobile-nav-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #374151;
  transition: transform 0.2s ease;
}

.mobile-nav-item:active {
  transform: scale(0.95);
}

.mobile-nav-icon {
  width: 48px;
  height: 48px;
  background-color: #f3f4f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: background-color 0.2s ease;
}

.mobile-nav-item:active .mobile-nav-icon {
  background-color: #e5e7eb;
}

.mobile-nav-icon svg {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

.mobile-nav-text {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 區段通用樣式 */
.mobile-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}

.mobile-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.mobile-view-all {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.mobile-view-all:active {
  color: #374151;
}

.mobile-view-all svg {
  width: 14px;
  height: 14px;
}

/* 分類區 */
.mobile-category-section {
  background-color: #ffffff;
  padding: 20px 0;
  margin-bottom: 20px;
}

.mobile-category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.mobile-category-card {
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  text-decoration: none;
  color: #374151;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.mobile-category-card:active {
  transform: scale(0.98);
  background-color: #f3f4f6;
}

.mobile-category-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.mobile-category-count {
  font-size: 14px;
  color: #6b7280;
}

/* 推薦商品區 */
.mobile-featured-section {
  background-color: #ffffff;
  padding: 20px 0;
  margin-bottom: 20px;
}

.mobile-product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.mobile-product-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.mobile-product-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.mobile-product-image-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.mobile-product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f3f4f6;
}

.mobile-product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.mobile-badge-new {
  background-color: #10b981;
}

.mobile-badge-hot {
  background-color: #f59e0b;
}

.mobile-product-info {
  padding: 12px;
}

.mobile-product-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mobile-product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-price-current {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.mobile-price-original {
  font-size: 14px;
  color: #9ca3af;
  text-decoration: line-through;
}

/* 品牌區 */
.mobile-brands-section {
  background-color: #ffffff;
  padding: 20px 0;
  margin-bottom: 20px;
}

.mobile-brands-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.mobile-brand-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #374151;
  transition: transform 0.2s ease;
}

.mobile-brand-card:active {
  transform: scale(0.95);
}

.mobile-brand-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
  background-color: #f3f4f6;
  margin-bottom: 8px;
}

.mobile-brand-name {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 載入狀態 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.mobile-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.mobile-loading-text {
  margin-top: 12px;
  font-size: 14px;
  color: #6b7280;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 響應式調整 */
@media (max-width: 480px) {
  .mobile-nav-grid {
    gap: 12px;
  }
  
  .mobile-nav-icon {
    width: 44px;
    height: 44px;
  }
  
  .mobile-nav-text {
    font-size: 11px;
  }
  
  .mobile-product-grid {
    gap: 8px;
  }
  
  .mobile-brands-grid {
    gap: 8px;
  }
  
  .mobile-brand-image {
    width: 50px;
    height: 50px;
  }
}
</style> 