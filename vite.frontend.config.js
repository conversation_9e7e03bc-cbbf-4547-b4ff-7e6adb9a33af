import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// 控制前後端連接的變數
const useLocalBackend = true  // 改為 false，使用遠端後端
const localBackendUrl = 'http://localhost:3003'  // 本地後端端口
const remoteBackendUrl = 'http://************:81'  // 設定為新的遠端後端網址和端口
const backendUrl = useLocalBackend ? localBackendUrl : remoteBackendUrl

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  root: path.resolve(__dirname, 'frontend'), // 設定根目錄為 frontend 目錄
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './frontend/src'),
    },
  },
  css: {
    // 啟用 CSS 模組
    modules: {
      localsConvention: 'camelCaseOnly',
    },
    // 啟用 CSS 預處理器
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  // 設定基礎路徑為相對路徑
  base: './',

  server: {
    port: 5110,  // 修改為指定的前台端口
    host: '0.0.0.0', // 允許外部訪問
    proxy: {
      '/api': {
        target: backendUrl,
        changeOrigin: true,
        secure: false,
        ws: true,
        configure: (proxy, options) => {
          // proxy.on('error', (err, req, res) => {
            // console.log('Frontend 代理錯誤:', err);
          // });
          // proxy.on('proxyReq', (proxyReq, req, res) => {
            // console.log('Frontend 代理請求:', req.method, req.url, '-> ', options.target + req.url);
          // });
        }
      },
      '/uploads': {
        target: backendUrl,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          // proxy.on('error', (err, req, res) => {
            // console.log('Frontend 上傳代理錯誤:', err);
          // });
        }
      }
    }
  }
})